#!/bin/bash
set -euo pipefail  # 严格模式：未定义变量报错、命令失败退出、管道错误传递

# 颜色定义，增强输出可读性
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # 无颜色

# 函数：打印错误信息并退出
error_exit() {
    echo -e "${RED}错误: $1${NC}" >&2
    exit 1
}

# 函数：检查依赖命令是否存在
check_dependency() {
    if ! command -v "$1" >/dev/null 2>&1; then
        echo -e "${YELLOW}正在安装 $1...${NC}"
        if ! apk add --no-cache "$1"; then
            error_exit "无法安装依赖 $1，请手动安装后重试"
        fi
    fi
}

# 定义选项 - 添加密码参数
SHORT=e:c:b:h:p:w:
LONG=env:,code:,branch:,host:,port:,password:,path:

# 解析命令行参数
echo -e "${YELLOW}解析命令行参数...${NC}"
OPTIONS=$(getopt -o "$SHORT" --long "$LONG" -n 'push.sh' -- "$@") || {
    error_exit "参数解析失败，请检查输入参数"
}
eval set -- "$OPTIONS"

# 初始化变量
APP_CODE=""
BRANCH="master"  # 默认分支
HOST=""
PORT=22        # 默认SSH端口
PASSWORD="Cc201819"    # SSH密码
BASE_PATH="/data/projects/biohub-deploy-cli/build/biohub-modules"  # 默认基础路径

# 处理选项
while true; do
    case "$1" in
        -c|--code)     APP_CODE="$2"; shift 2 ;;
        -b|--branch)   BRANCH="$2"; shift 2 ;;
        -h|--host)     HOST="$2"; shift 2 ;;
        -p|--port)     PORT="$2"; shift 2 ;;  # 端口参数
        -w|--password) PASSWORD="$2"; shift 2 ;;  # 密码参数
        --path)        BASE_PATH="$2"; shift 2 ;;  # 路径参数
        --) shift; break ;;
        *) error_exit "意外选项: $1" ;;
    esac
done

# 参数验证
echo -e "${YELLOW}验证参数...${NC}"
[ -z "$APP_CODE" ] && error_exit "必须通过 -c/--code 指定应用代码"
[ -z "$HOST" ] && error_exit "必须通过 -h/--host 指定目标主机"
[ -z "$PASSWORD" ] && error_exit "必须通过 -w/--password 指定SSH密码"

# 计算路径变量
CODE_DIR="${BASE_PATH}/${BRANCH}"
JAR_SOURCE_DIR="${CODE_DIR}/biohub-modules-${APP_CODE}/target"
DEST_JAR_NAME="ysh.${APP_CODE}.jar"
DEST_JAR_PATH="root@${HOST}:/data/ysh/jar-run/app/"
DEST_LIB_PATH="root@${HOST}:/data/ysh/jar-run/app/lib/${APP_CODE}/"
SOURCE_JAR_PATH="${JAR_SOURCE_DIR}/${DEST_JAR_NAME}"

# 验证源文件是否存在
echo -e "${YELLOW}验证构建产物...${NC}"
[ ! -d "$JAR_SOURCE_DIR" ] && error_exit "JAR源目录不存在: $JAR_SOURCE_DIR"
[ ! -f "$SOURCE_JAR_PATH" ] && error_exit "未找到JAR文件: $SOURCE_JAR_PATH"
[ ! -d "${JAR_SOURCE_DIR}/lib" ] && error_exit "依赖库目录不存在: ${JAR_SOURCE_DIR}/lib"

# 显示参数信息
echo -e "${GREEN}推送参数确认:${NC}"
echo "  应用代码:    $APP_CODE"
echo "  分支:        $BRANCH"
echo "  目标主机:    $HOST"
echo "  目标端口:    $PORT"
echo "  基础路径:    $BASE_PATH"
echo "  JAR源路径:   $SOURCE_JAR_PATH"
echo "  远程JAR路径: $DEST_JAR_PATH"
echo "  远程LIB路径: $DEST_LIB_PATH"

# 检查依赖，增加sshpass检查
check_dependency "rsync"
check_dependency "ssh"
check_dependency "sshpass"
check_dependency "scp"

# 部署JAR包，使用sshpass进行认证
echo -e "\n${YELLOW}部署文件到目标主机...${NC}"
echo "同步JAR包到远程..."
sshpass -p "$PASSWORD" rsync -rav --delete \
  -e "ssh -p $PORT -o StrictHostKeyChecking=no" \
  "$SOURCE_JAR_PATH" "$DEST_JAR_PATH" || 
  error_exit "JAR包同步失败"

# 部署依赖库
echo "创建远程库目录..."
sshpass -p "$PASSWORD" ssh -p "$PORT" -o StrictHostKeyChecking=no "root@${HOST}" \
  "mkdir -p '${DEST_LIB_PATH}'" || 
  error_exit "无法创建远程库目录"

echo "同步依赖库到远程..."
sshpass -p "$PASSWORD" rsync -rav --delete \
  -e "ssh -p $PORT -o StrictHostKeyChecking=no" \
  "${JAR_SOURCE_DIR}/lib/" "$DEST_LIB_PATH" || 
  error_exit "依赖库同步失败"

# 执行远程启动脚本
echo -e "\n${YELLOW}执行远程部署脚本...${NC}"
sshpass -p "$PASSWORD" ssh -p "$PORT" -o StrictHostKeyChecking=no "root@${HOST}" \
  "/data/ysh/jar-run/app/run.sh $APP_CODE unkeep" || 
  error_exit "远程部署脚本执行失败"

echo -e "\n${GREEN}===== 推送完成 =====${NC}"
