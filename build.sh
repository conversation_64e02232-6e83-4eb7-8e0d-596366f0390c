#!/bin/bash
set -euo pipefail  # 严格模式：未定义变量报错、命令失败退出、管道错误传递

# 颜色定义，增强输出可读性
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # 无颜色

# 函数：打印错误信息并退出
error_exit() {
    echo -e "${RED}错误: $1${NC}" >&2
    exit 1
}

# 函数：检查依赖命令是否存在
check_dependency() {
    if ! command -v "$1" >/dev/null 2>&1; then
        echo -e "${YELLOW}正在安装 $1...${NC}"
        if ! apk add --no-cache "$1"; then
            error_exit "无法安装依赖 $1，请手动安装后重试"
        fi
    fi
}

# 定义选项
SHORT=b:c:g:h:
LONG=branch:,code:,giturl:,host:

# 解析命令行参数
echo -e "${YELLOW}解析命令行参数...${NC}"
OPTIONS=$(getopt -o "$SHORT" --long "$LONG" -n 'build.sh' -- "$@") || {
    error_exit "参数解析失败，请检查输入参数"
}
eval set -- "$OPTIONS"

# 初始化变量
BRANCH="master"  # 设置默认分支
APP_CODE=""
HOST=""
GIT_URL="http://yshRep01:Rb29t%402022@**************:8880/ysh/biohub-modules.git"

# 处理选项
while true; do
    case "$1" in
        -b|--branch)   BRANCH="$2"; shift 2 ;;
        -c|--code)     APP_CODE="$2"; shift 2 ;;
        --) shift; break ;;
        *) error_exit "意外选项: $1" ;;
    esac
done

# 参数验证
echo -e "${YELLOW}验证必要参数...${NC}"
[ -z "$APP_CODE" ] && error_exit "必须通过 -c/--code 指定应用代码"

# 显示参数信息
echo -e "${GREEN}构建参数确认:${NC}"
echo "  分支:        $BRANCH"
echo "  应用代码:    $APP_CODE"
echo "  Git地址:     $GIT_URL"

# 定义路径变量
BASE_DIR="/data/projects/biohub-deploy-cli/build/biohub-modules"
CODE_DIR="${BASE_DIR}/${BRANCH}"
JAR_SOURCE_DIR="${CODE_DIR}/biohub-modules-${APP_CODE}/target"

# 检查依赖
check_dependency "git"
check_dependency "mvn"  # 假设maven已预安装，若需动态安装可调整

# 拉取/更新代码
echo -e "\n${YELLOW}处理代码仓库...${NC}"
if [ ! -d "$CODE_DIR" ]; then
    echo "首次拉取 $BRANCH 分支代码..."
    mkdir -p "$BASE_DIR" || error_exit "无法创建基础目录 $BASE_DIR"
    git clone --single-branch --branch "$BRANCH" "$GIT_URL" "$CODE_DIR" || 
        error_exit "代码克隆失败"
else
    echo "更新 $BRANCH 分支代码..."
    (cd "$CODE_DIR" && 
        git checkout . && 
        git pull) || error_exit "代码更新失败"
fi

# 构建项目
echo -e "\n${YELLOW}开始构建项目...${NC}"
(cd "$CODE_DIR" && 
    echo "当前工作目录: $(pwd)" &&
    mvn -s ~/.m2/settings.xml clean install -DskipTests \
        -pl "com.ysh:biohub-modules-${APP_CODE}" -am) || 
    error_exit "Maven构建失败"

# 处理生成的JAR包
echo -e "\n${YELLOW}处理构建产物...${NC}"
JAR_FILE=$(find "$JAR_SOURCE_DIR" -maxdepth 1 -name "*.jar" ! -name "*-tests.jar" | head -n 1)

[ -z "$JAR_FILE" ] && error_exit "未找到构建生成的JAR文件"

# 重命名JAR包
if [ "$JAR_FILE" != "${JAR_SOURCE_DIR}/ysh.${APP_CODE}.jar" ]; then
    echo "重命名JAR包..."
    mv "$JAR_FILE" "${JAR_SOURCE_DIR}/ysh.${APP_CODE}.jar" -f
fi

# 构建成功
echo -e "\n${GREEN}===== 构建完成 =====${NC}"
echo "构建产物位置: ${JAR_SOURCE_DIR}/ysh.${APP_CODE}.jar"
echo "Library位置: ${JAR_SOURCE_DIR}/lib"
